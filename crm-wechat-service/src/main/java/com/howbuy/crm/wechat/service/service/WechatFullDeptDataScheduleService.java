package com.howbuy.crm.wechat.service.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.dao.po.CmWechatDeptPO;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import com.howbuy.crm.wechat.service.business.WechatDataBusiness;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.repository.CmWechatDeptRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatEmpRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by shucheng on 2022/1/26 16:41
 */
@Slf4j
@Service
@EnableScheduling
@Transactional
public class WechatFullDeptDataScheduleService {

    /** sql的in里每隔 IN_INTERVAL_SIZE 个数进行拆分 */
    @Autowired
    private WechatDataBusiness wechatDataBusiness;
    @Autowired
    private CmWechatDeptRepository cmWechatDeptRepository;
    @Autowired
    private CmWechatEmpRepository cmWechatEmpRepository;

    /**
     * @description:全量处理员工与部门数据
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 16:14
     * @since JDK 1.8
     */
    public void execute() {
        List<String> companyNoEnums = Lists.newArrayList(Constants.COMPANY_NO_HOWBUY_FUND, Constants.DEFAULT_COMPANY_NO);
        executeSync(companyNoEnums);
    }

    /**
     * @description:同步部门、员工数据
     * @param companyNoEnums
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 16:14
     * @since JDK 1.8
     */
    public void executeSync(List<String> companyNoEnums) {
        log.info("待同步公司：{} ，部门、员工数据全量比对 START",JSON.toJSONString(companyNoEnums));
        companyNoEnums.forEach(companyNoEnum -> {
            syncDeportAndEmp(companyNoEnum);
        });
    }

    /**
     * @description:全量比对后插入,同步部门、员工数据
     * @param companyNoEnum
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 16:14
     * @since JDK 1.8
     */
    private void syncDeportAndEmp(String companyNo) {
        log.info("companyNo:{}, 部门、员工数据全量比对 START", companyNo);

        // 处理部门数据
        List<CmWechatDeptPO> fullDeptList = wechatDataBusiness.getFullDeptListByCompanyNo(companyNo);
        if (CollectionUtils.isNotEmpty(fullDeptList)) {
            int  deleteCount = cmWechatDeptRepository.removeStaleWechatDept(companyNo,fullDeptList);
            log.info("companyNo:{}, 部门表中过时数据标记删除，删除条数：{}", companyNo, deleteCount);

            // 然后再用全量数据merge到表中数据
            int mergeCount=cmWechatDeptRepository.batchMergeWechatDept(fullDeptList);
            log.info("companyNo:{}, 部门表数据merge成功条数：{}", companyNo, mergeCount);
        }

        // 处理员工数据
        List<CmWechatEmpPO> fullDeptUserDetailList = wechatDataBusiness.getFullDeptUserDetailListByCompanyNo(companyNo);
        if (CollectionUtils.isNotEmpty(fullDeptUserDetailList)) {
            log.info("companyNo:{}, compareInsertAll|fullDeptUserDetailList:{}",companyNo, JSON.toJSONString(fullDeptUserDetailList));
            // 先利用全量数据找出表中未更新状态的离职员工，把表中的离职员工置为删除状态
            cmWechatEmpRepository.removeStaleWechatEmp(companyNo,fullDeptUserDetailList);
            log.info("companyNo:{}, 员工表中过时数据更新", companyNo);

            // 然后再用全量数据merge到表中数据
            cmWechatEmpRepository.batchMergeWechatEmp(fullDeptUserDetailList);
            log.info("companyNo:{}, 员工表数据merge成功", companyNo);
        }

        log.info("companyNo:{}, 部门、员工数据全量比对 END", companyNo);
    }



}
