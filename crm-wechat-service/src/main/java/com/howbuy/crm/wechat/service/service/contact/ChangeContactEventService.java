package com.howbuy.crm.wechat.service.service.contact;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.Assert;
import com.howbuy.crm.wechat.dao.po.CmWechatDeptPO;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import com.howbuy.crm.wechat.service.commom.constant.WxTempConstant;
import com.howbuy.crm.wechat.service.commom.utils.WechatCorpUtil;
import com.howbuy.crm.wechat.service.domain.callback.ContactDTO;
import com.howbuy.crm.wechat.service.repository.CmWechatDeptRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatEmpRepository;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 通讯录回调处理service
 * @author: yu.zhang
 * @date: 2023/6/12 10:17 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class ChangeContactEventService {

    @Autowired
    private CmWechatDeptRepository cmWechatDeptRepository;

    @Autowired
    private CmWechatEmpRepository cmWechatEmpRepository;

    @Autowired
    private BaseConfigServce baseConfigServce;

    public static final String LOG_START_STR = "触发【通讯录回调-{}】事件 START：{}";
    public static final String LOG_END_STR = "触发【通讯录回调-{}】事件 END：{}";
    /**
     * 头像缩略图url最后尾部常量
     */
    private static final String THUMB_AVATAR_END_CHAR ="100";
    /**
     * 头像url最后尾部常量
     */
    private static final String AVATAR_END_CHAR ="0";

    /**
     * @description:通讯录回调事件
     * @param contactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:14
     * @since JDK 1.8
     */
    public void processContactsChangeType(ContactDTO contactDTO) {
        Assert.notNull(contactDTO.getCorpId(), "企业ID不能为空！");
        switch (contactDTO.getChangeType()) {
            case WxTempConstant.CHANGE_TYPE_CREATE_USER:
                // 新增成员
                this.handleCreateUserChangeType(contactDTO);
                break;
            case WxTempConstant.CHANGE_TYPE_UPDATE_USER:
                // 修改成员
                this.handleUpdateUserChangeType(contactDTO);
                break;
            case WxTempConstant.CHANGE_TYPE_DELETE_USER:
                // 删除成员
                this.handleDeleteUserChangeType(contactDTO);
                break;
            case WxTempConstant.CHANGE_TYPE_CREATE_PARTY:
                // 新增部门
                this.handleCreatePartyChangeType(contactDTO);
                break;
            case WxTempConstant.CHANGE_TYPE_UPDATE_PARTY:
                // 修改部门
                this.handleUpdatePartyChangeType(contactDTO);
                break;
            case WxTempConstant.CHANGE_TYPE_DELETE_PARTY:
                // 删除部门
                this.handleDeletePartyChangeType(contactDTO);
                break;
            default:
                log.info("processContactsChangeType changeType不触发数据处理:{}", JSON.toJSONString(contactDTO));
        }
    }


    private String getStringByCorpId(String corpId) {
        return baseConfigServce.getString(corpId);
    }

    /**
     * @description:处理“新增成员”回调
     * @param contactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 10:43
     * @since JDK 1.8
     */
    private void handleCreateUserChangeType(ContactDTO contactDTO) {
        log.info(LOG_START_STR, "新增成员", contactDTO);
        CmWechatEmpPO wechatEmp = new CmWechatEmpPO();
        wechatEmp.setEmpId(contactDTO.getUserID());
        wechatEmp.setEmpName(contactDTO.getName());
        wechatEmp.setDeptId(contactDTO.getMainDepartment());
        wechatEmp.setCompanyNo(getStringByCorpId(contactDTO.getCorpId()));
        wechatEmp.setAvatar(contactDTO.getAvatar());
        thumbAvatarHandle(wechatEmp);
        cmWechatEmpRepository.insertWechatEmp(wechatEmp);
        log.info("员工信息插入成功，data：{}", wechatEmp);
        log.info(LOG_END_STR, "新增成员", contactDTO);
    }

    /**
     * @description:处理“修改成员”回调
     * @param contactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 10:43
     * @since JDK 1.8
     */
    private void handleUpdateUserChangeType(ContactDTO contactDTO) {

        log.info(LOG_START_STR, "修改成员", contactDTO);


        String empId = contactDTO.getUserID();
        String newEmpId = contactDTO.getNewUserID();
        String empName = contactDTO.getName();
        Integer deptId = contactDTO.getMainDepartment();

        if (!ObjectUtil.isAllEmpty(newEmpId, empName, deptId)) {
            CmWechatEmpPO wechatEmp = new CmWechatEmpPO();
            wechatEmp.setEmpId(empId);
            wechatEmp.setEmpName(empName);
            wechatEmp.setDeptId(deptId);
            wechatEmp.setCompanyNo(getStringByCorpId(contactDTO.getCorpId()));
            wechatEmp.setAvatar(contactDTO.getAvatar());
            thumbAvatarHandle(wechatEmp);
            cmWechatEmpRepository.updateWechatEmpByEmpId(wechatEmp,newEmpId);
        }

        log.info(LOG_END_STR, "修改成员", contactDTO);
    }

    /**
     * 头像缩略图处理处理
     * @param wechatEmp
     */
    private void thumbAvatarHandle(CmWechatEmpPO wechatEmp){
        String avatar = wechatEmp.getAvatar();
        if(StringUtils.isNotEmpty(avatar)){
            //如果要获取小图将头像url最后的”/0”改成”/100”，即0改成100
            if(AVATAR_END_CHAR.equals(avatar.substring(avatar.length() - 1))){
                wechatEmp.setThumbAvatar(avatar.substring(0, avatar.length() - 1) + THUMB_AVATAR_END_CHAR);
            }else{
                wechatEmp.setThumbAvatar(avatar + THUMB_AVATAR_END_CHAR);
            }
        }
    }

    /**
     * @description:处理“删除成员”回调
     * @param contactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 10:43
     * @since JDK 1.8
     */
    private void handleDeleteUserChangeType(ContactDTO contactDTO) {
        log.info(LOG_START_STR, "删除成员", contactDTO);

        String empId = contactDTO.getUserID();
        String companyNo = getStringByCorpId(contactDTO.getCorpId());
        cmWechatEmpRepository.delWechatEmpByEmpId(empId,companyNo);

        log.info(LOG_END_STR, "删除成员", contactDTO);
    }

    /**
     * @description:处理“新增部门”回调
     * @param contactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 10:42
     * @since JDK 1.8
     */
    private void handleCreatePartyChangeType(ContactDTO contactDTO) {
        log.info(LOG_START_STR, "新增部门", contactDTO);

        CmWechatDeptPO wechatDept = new CmWechatDeptPO();
        wechatDept.setDeptId(contactDTO.getId());
        wechatDept.setDeptName(contactDTO.getName());
        wechatDept.setParentDeptId(contactDTO.getParentId());
        wechatDept.setCompanyNo(getStringByCorpId(contactDTO.getCorpId()));
        cmWechatDeptRepository.insertWechatDept(wechatDept);

        log.info(LOG_END_STR, "新增部门", contactDTO);
    }

    /**
     * @description:处理“修改部门”回调
     * @param contactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:43
     * @since JDK 1.8
     */
    private void handleUpdatePartyChangeType(ContactDTO contactDTO) {
        Assert.notNull(contactDTO.getId(), "部门不能为空！");

        log.info(LOG_START_STR, "修改部门", contactDTO);

        CmWechatDeptPO wechatDept = new CmWechatDeptPO();
        wechatDept.setDeptId(contactDTO.getId());
        wechatDept.setDeptName(contactDTO.getName());
        wechatDept.setParentDeptId(contactDTO.getParentId());
        wechatDept.setCompanyNo(getStringByCorpId(contactDTO.getCorpId()));
        cmWechatDeptRepository.updateWechatDeptByDeptId(wechatDept);

        log.info(LOG_END_STR, "修改部门", contactDTO);
    }

    /**
     * @description:处理“删除部门”回调
     * @param contactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:28
     * @since JDK 1.8
     */
    private void handleDeletePartyChangeType(ContactDTO contactDTO) {
        log.info(LOG_START_STR, "删除部门", contactDTO);

        Integer deptId = contactDTO.getId();
        String companyNo = getStringByCorpId(contactDTO.getCorpId());
        cmWechatDeptRepository.delWechatDeptByDeptId(deptId,companyNo);
        log.info("部门信息删除成功，deptId：{}", deptId);

        log.info(LOG_END_STR, "删除部门", contactDTO);
    }

}
